#include "VoltageVerificationWorker.h"
#include <QApplication>
#include <QThread>
#include <cmath>

// 静态常量定义
const QVector<VoltageVerificationWorker::VoltageLevel> VoltageVerificationWorker::VOLTAGE_LEVELS = {
    {-10.0, "-10mV"},
    {10.0, "10mV"},
    {30.0, "30mV"},
    {50.0, "50mV"},
    {75.0, "75mV"}};

const double VoltageVerificationWorker::VOLTAGE_COLLECTION_INTERVAL = 1000; // 1秒间隔
const double VoltageVerificationWorker::STABILIZATION_TIME = 2000;          // 2秒稳定时间

VoltageVerificationWorker::VoltageVerificationWorker(QObject *parent)
    : QObject(parent),
      m_abortRequested(false),
      m_isRecalibrationMode(false),
      m_dataCollectionTimer(new QTimer(this)),
      m_stabilizationTimer(new QTimer(this)),
      m_currentLevel(0),
      m_currentChannel(0),
      m_currentReading(0),
      m_adjCommandHandler(nullptr)
{
    m_dataCollectionTimer->setSingleShot(true);
    m_stabilizationTimer->setSingleShot(true);

    // Timer不再需要连接到collectVoltageData
    connect(m_stabilizationTimer, &QTimer::timeout, this, &VoltageVerificationWorker::startDataCollection);
}

VoltageVerificationWorker::~VoltageVerificationWorker()
{
    m_abortRequested = true;
}

void VoltageVerificationWorker::setDeviceConfig(const AdjDeviceConfig &config)
{
    m_deviceConfig = config;
}

void VoltageVerificationWorker::setCommandHandlers(CommandHandlerFunc adjHandler)
{
    m_adjCommandHandler = adjHandler;
}

void VoltageVerificationWorker::requestAbort()
{
    m_abortRequested = true;
    if (m_dataCollectionTimer->isActive())
    {
        m_dataCollectionTimer->stop();
    }
    if (m_stabilizationTimer->isActive())
    {
        m_stabilizationTimer->stop();
    }

    emit logMessage("电压校准验证被用户中止");
    m_isRecalibrationMode = false; // 重置复校模式标志
    emit voltageVerificationFinished(false);
}

void VoltageVerificationWorker::startVoltageVerification()
{
    emit logMessage("开始1618A TC电压校准验证...");
    emit logMessage(QString("设备型号: %1").arg(m_deviceConfig.name));
    emit logMessage(QString("通道数量: %1").arg(m_deviceConfig.num_channels));

    m_abortRequested = false;
    m_currentLevel = 0;
    m_currentChannel = 0;
    m_currentReading = 0;
    m_voltageReadings.clear();
    m_isRecalibrationMode = false;

    // 计算总步数：通道数 × 5个档位
    int totalSteps = m_deviceConfig.num_channels * VOLTAGE_LEVELS.size();
    emit voltageVerificationProgress(0, totalSteps);
    emit voltageVerificationStarted();

    // 开始第一个电压档位的第一个通道校准
    showUserPrompt(VOLTAGE_LEVELS[m_currentLevel].targetVoltage, m_currentChannel + 1);
}

void VoltageVerificationWorker::onUserPromptResult(bool confirmed)
{
    if (!confirmed)
    {
        emit logMessage("用户取消了电压校准验证");
        m_isRecalibrationMode = false; // 重置复校模式标志
        emit voltageVerificationFinished(false);
        return;
    }

    if (m_abortRequested)
    {
        emit logMessage("电压校准验证被用户中止");
        m_isRecalibrationMode = false; // 重置复校模式标志
        emit voltageVerificationFinished(false);
        return;
    }

    emit logMessage(QString("开始校准 CH%1 - %2")
                        .arg(m_currentChannel + 1)
                        .arg(VOLTAGE_LEVELS[m_currentLevel].description));

    // 开始稳定时间计时
    startStabilizationTimer();
}

void VoltageVerificationWorker::showUserPrompt(double targetVoltage, int channel)
{
    QString title = QString("CH%1电压校准 - %2").arg(channel).arg(VOLTAGE_LEVELS[m_currentLevel].description);
    QString message = QString("请执行以下操作：\n\n"
                              "1. 将754校准器连接至通道%1\n"
                              "2. 设置754输出电压值：%2mV\n"
                              "3. 设置完成后点击 确定 继续\n"
                              "4. 点击 取消 退出校准流程")
                          .arg(channel)
                          .arg(targetVoltage, 0, 'f', 1);

    emit requestUserPrompt(title, message);
}

void VoltageVerificationWorker::startStabilizationTimer()
{
    emit logMessage(QString("等待稳定时间 %1 秒...").arg(STABILIZATION_TIME / 1000.0, 0, 'f', 1));
    m_stabilizationTimer->start(STABILIZATION_TIME);
}

void VoltageVerificationWorker::startDataCollection()
{
    m_voltageReadings.clear();
    m_currentReading = 0;

    emit logMessage("开始数据采集...");

    // 使用和VerificationWorker相同的方式进行4次读取
    auto [passed, result] = read_voltages(m_currentChannel, 1);
    if (passed)
    {
        m_voltageReadings = result;
        processCollectedData();
    }
    else
    {
        emit logMessage(QString("复校模式标志: %1").arg(m_isRecalibrationMode ? "true" : "false")); // 调试日志
        if (m_isRecalibrationMode)
        {
            emit logMessage("复校数据采集失败，复校结束");
            m_isRecalibrationMode = false; // 重置复校模式标志
            emit voltageVerificationFinished(false);
        }
        else
        {
            emit logMessage("数据采集失败，跳过当前档位");
            moveToNextLevel();
        }
    }
}

// moveToNextReading函数不再需要，因为我们使用read_voltages一次性读取4个值

void VoltageVerificationWorker::processCollectedData()
{
    if (m_abortRequested)
    {
        emit logMessage("电压校准验证被用户中止");
        m_isRecalibrationMode = false; // 重置复校模式标志
        emit voltageVerificationFinished(false);
        return;
    }

    // 即使数据不足4个，也继续处理，避免卡死
    if (m_voltageReadings.isEmpty())
    {
        emit logMessage(QString("复校模式标志: %1").arg(m_isRecalibrationMode ? "true" : "false")); // 调试日志
        if (m_isRecalibrationMode)
        {
            emit logMessage("复校失败: 无有效数据，复校结束");
            m_isRecalibrationMode = false; // 重置复校模式标志
            emit voltageVerificationFinished(false);
        }
        else
        {
            emit logMessage("警告: 当前档位没有有效数据，跳过此档位");
            moveToNextLevel();
        }
        return;
    }

    // 如果数据不足4个，用现有数据的平均值
    if (m_voltageReadings.size() < 4)
    {
        emit logMessage(QString("警告: 只获取到 %1 个有效数据，将基于这些数据计算").arg(m_voltageReadings.size()));
    }

    // 计算平均值
    double sum = 0.0;
    for (double voltage : m_voltageReadings)
    {
        sum += voltage;
    }
    double measuredVoltage = sum / m_voltageReadings.size(); // 使用实际数据个数

    // 计算偏差
    double referenceVoltage = VOLTAGE_LEVELS[m_currentLevel].targetVoltage;
    double deviation = measuredVoltage - referenceVoltage;

    // 计算允差
    double allowedDeviation = calculateAllowedDeviation(measuredVoltage);

    // 判断是否合格
    bool verificationResult = isWithinTolerance(std::abs(deviation), allowedDeviation);

    emit logMessage(QString("CH%1 %2 校准完成:")
                        .arg(m_currentChannel + 1)
                        .arg(VOLTAGE_LEVELS[m_currentLevel].description));
    emit logMessage(QString("  测量值: %1 mV").arg(measuredVoltage, 0, 'f', 6));
    emit logMessage(QString("  偏差: %1 mV").arg(deviation, 0, 'f', 6));
    emit logMessage(QString("  允差: ±%1 mV").arg(allowedDeviation, 0, 'f', 6));
    emit logMessage(QString("  结果: %1").arg(verificationResult ? "合格" : "不合格"));

    // 发送数据更新信号
    emit updateVoltageChannelData(referenceVoltage, m_currentChannel + 1,
                                  m_voltageReadings, measuredVoltage,
                                  deviation, allowedDeviation, verificationResult);

    // 如果是复校模式，直接结束；否则移动到当前通道的下一个档位
    if (m_isRecalibrationMode)
    {
        emit logMessage("复校完成");
        m_isRecalibrationMode = false; // 重置复校模式标志
        emit voltageVerificationFinished(true);
    }
    else
    {
        // 更新进度（仅在非复校模式下）
        int currentStep = m_currentChannel * VOLTAGE_LEVELS.size() + m_currentLevel + 1;
        int totalSteps = m_deviceConfig.num_channels * VOLTAGE_LEVELS.size();
        emit voltageVerificationProgress(currentStep, totalSteps);

        // 移动到当前通道的下一个档位
        moveToNextLevel();
    }
}

void VoltageVerificationWorker::moveToNextLevel()
{
    if (m_abortRequested)
    {
        emit logMessage("电压校准验证被用户中止");
        m_isRecalibrationMode = false; // 重置复校模式标志
        emit voltageVerificationFinished(false);
        return;
    }

    m_currentLevel++;

    if (m_currentLevel < VOLTAGE_LEVELS.size())
    {
        // 继续当前通道的下一个档位
        m_currentReading = 0;
        m_voltageReadings.clear();

        // 更新进度条（即使数据采集失败也要更新进度）
        int currentStep = m_currentChannel * VOLTAGE_LEVELS.size() + m_currentLevel + 1;
        int totalSteps = m_deviceConfig.num_channels * VOLTAGE_LEVELS.size();
        emit voltageVerificationProgress(currentStep, totalSteps);

        showUserPrompt(VOLTAGE_LEVELS[m_currentLevel].targetVoltage, m_currentChannel + 1);
    }
    else
    {
        // 当前通道所有档位完成，移动到下一个通道
        moveToNextChannel();
    }
}

void VoltageVerificationWorker::moveToNextChannel()
{
    if (m_abortRequested)
    {
        emit logMessage("电压校准验证被用户中止");
        m_isRecalibrationMode = false; // 重置复校模式标志
        emit voltageVerificationFinished(false);
        return;
    }

    m_currentChannel++;

    if (m_currentChannel < m_deviceConfig.num_channels)
    {
        // 继续下一个通道，重置档位到第一个
        m_currentLevel = 0;
        m_currentReading = 0;
        m_voltageReadings.clear();

        // 更新进度条（切换到下一个通道时）
        int currentStep = m_currentChannel * VOLTAGE_LEVELS.size() + m_currentLevel + 1;
        int totalSteps = m_deviceConfig.num_channels * VOLTAGE_LEVELS.size();
        emit voltageVerificationProgress(currentStep, totalSteps);

        showUserPrompt(VOLTAGE_LEVELS[0].targetVoltage, m_currentChannel + 1);
    }
    else
    {
        // 所有通道完成
        finishVerification();
    }
}

void VoltageVerificationWorker::restartVoltageVerification(int levelIndex, double referenceVoltage, int channel)
{
    // 重置中止标志，确保复校可以正常进行
    m_abortRequested = false;

    // 停止所有可能正在运行的定时器，确保状态清洁
    if (m_dataCollectionTimer->isActive())
    {
        m_dataCollectionTimer->stop();
    }
    if (m_stabilizationTimer->isActive())
    {
        m_stabilizationTimer->stop();
    }

    emit logMessage(QString("开始电压复校：档位 %1mV，通道 %2")
                        .arg(referenceVoltage)
                        .arg(channel));

    // 设置复校状态
    m_currentLevel = levelIndex;
    m_currentChannel = channel - 1; // 转换为0基索引
    m_currentReading = 0;
    m_voltageReadings.clear();
    m_isRecalibrationMode = true; // 标记为复校模式

    emit logMessage("复校模式已启用"); // 调试日志

    // 开始复校流程
    showUserPrompt(referenceVoltage, channel);
}

void VoltageVerificationWorker::finishVerification()
{
    emit logMessage("电压校准验证完成");

    // 确保进度条达到100%
    int totalSteps = m_deviceConfig.num_channels * VOLTAGE_LEVELS.size();
    emit voltageVerificationProgress(totalSteps, totalSteps);

    emit voltageVerificationFinished(true);
}

double VoltageVerificationWorker::calculateAllowedDeviation(double measuredVoltage) const
{
    // 允差计算公式：0.0001 × 测量电压值 + 0.01
    return 0.0001 * std::abs(measuredVoltage) + 0.01;
}

bool VoltageVerificationWorker::isWithinTolerance(double deviation, double allowedDeviation) const
{
    return deviation <= allowedDeviation;
}

// 电压校准专用读取函数
double VoltageVerificationWorker::read_voltage(int channel)
{
    // 创建电压读取命令: 01 03 [地址] 00 04 (读4个寄存器)
    QByteArray frame = createVoltageReadFrame(0x01, m_deviceConfig.read_addr, channel);

    QPair<bool, QString> result = sendCommand(
        createModbusCommand(QString::fromLatin1(frame.toHex())),
        "Cal_ReadARoundRVals");

    double voltage;
    if (result.first)
    {
        double value = result.second.toDouble();
        voltage = value;
    }
    else
    {
        emit logMessage(QString("读取通道 %1 数据失败: %2").arg(channel + 1).arg(result.second));
        voltage = 0.0; // 错误时返回默认值
    }

    return voltage; // 读取单通道值
}

QPair<bool, QVector<double>> VoltageVerificationWorker::read_voltages(int channel, int attempt)
{
    emit logMessage(QString("通道 %1 第 %2 次读取 4 轮电压值开始...").arg(channel + 1).arg(attempt));

    // 读取4轮电压数据
    QVector<double> voltages;

    double lastVoltage = 0.0;
    for (int round = 0; round < 4; ++round)
    {
        if (m_abortRequested)
            return QPair<bool, QVector<double>>(false, QVector<double>(4, 0.0));

        double voltage = read_voltage(channel); // channel = 0 -> CH1
        if (voltage == 0.0)
        {
            return QPair<bool, QVector<double>>(false, QVector<double>(4, 0.0));
        }

        lastVoltage = voltage;

        qDebug() << "lastVoltage: " << lastVoltage;
        voltages.append(lastVoltage);

        // 使用可中断等待，提高中止响应性
        if (!interruptibleMSleep(1000))
        {
            return QPair<bool, QVector<double>>(false, QVector<double>(4, 0.0));
        }
    }

    return QPair<bool, QVector<double>>(true, voltages);
}

bool VoltageVerificationWorker::interruptibleSleep(int seconds)
{
    for (int i = 0; i < seconds; ++i)
    {
        if (m_abortRequested)
            return false;
        QThread::sleep(1);
    }
    return true;
}

bool VoltageVerificationWorker::interruptibleMSleep(int milliseconds)
{
    int sleepInterval = 100; // 每次睡眠100毫秒
    int iterations = milliseconds / sleepInterval;
    int remainder = milliseconds % sleepInterval;

    for (int i = 0; i < iterations; ++i)
    {
        if (m_abortRequested)
            return false;
        QThread::msleep(sleepInterval);
    }

    if (remainder > 0)
    {
        if (m_abortRequested)
            return false;
        QThread::msleep(remainder);
    }

    return true;
}

// 电压校准专用命令帧创建函数
// 格式: 01 03 [地址] 00 04 (固定读4个寄存器，8字节双精度数据)
QByteArray VoltageVerificationWorker::createVoltageReadFrame(uint8_t deviceAddr, uint16_t baseAddr, int channel)
{
    // 计算通道地址：每个通道占用4个寄存器（双精度）
    uint16_t addr = baseAddr + channel * 4;
    uint8_t addr_high = (addr >> 8) & 0xFF; // 提取高字节
    uint8_t addr_low = addr & 0xFF;         // 提取低字节

    // 构造 Modbus RTU 请求帧（不含 CRC）
    // 格式: 01 03 00 3D 00 04 (读4个寄存器)
    QByteArray frame;
    frame.append(static_cast<char>(deviceAddr)); // 01: 设备地址
    frame.append(static_cast<char>(0x03));       // 03: 功能码（读保持寄存器）
    frame.append(static_cast<char>(addr_high));  // 地址高字节
    frame.append(static_cast<char>(addr_low));   // 地址低字节
    frame.append(static_cast<char>(0x00));       // 寄存器数量高字节
    frame.append(static_cast<char>(0x04));       // 04: 读4个寄存器

    return frame;
}

QPair<bool, QString> VoltageVerificationWorker::sendCommand(const QByteArray &command, const QString &commandType)
{
    // 根据命令类型选择合适的命令处理函数
    if (commandType.startsWith("Cal_"))
    {
        if (m_adjCommandHandler)
        {
            return m_adjCommandHandler(command, commandType);
        }
    }

    return {false, "未知的命令类型或命令处理函数未设置"};
}

QByteArray VoltageVerificationWorker::createModbusCommand(const QString &hexCommand)
{
    // 创建基础RTU数据（不含CRC）
    QByteArray rtuData = createModbusRtuFrame(hexCommand);

    // 根据设备型号选择协议格式
    if (isTcpDevice(m_deviceConfig.name))
    {
        // TCP设备：创建TCP帧格式
        return createModbusTcpFrame(rtuData);
    }
    else
    {
        // RTU设备：添加CRC校验
        uint16_t crc = calculateCRC16(rtuData);
        rtuData.append(static_cast<char>(crc & 0xFF));
        rtuData.append(static_cast<char>((crc >> 8) & 0xFF));
        return rtuData;
    }
}

uint16_t VoltageVerificationWorker::calculateCRC16(const QByteArray &data)
{
    uint16_t crc = 0xFFFF;
    for (char ch : data)
    {
        crc ^= static_cast<uint8_t>(ch);
        for (int i = 0; i < 8; ++i)
        {
            if (crc & 0x0001)
            {
                crc = (crc >> 1) ^ 0xA001;
            }
            else
            {
                crc = crc >> 1;
            }
        }
    }
    return crc;
}

// TCP协议支持函数实现
bool VoltageVerificationWorker::isTcpDevice(const QString &deviceModel)
{
    // 定义使用TCP协议的设备型号列表
    QStringList tcpDevices = {"TM14ND-P-LAN"};
    return tcpDevices.contains(deviceModel);
}

QByteArray VoltageVerificationWorker::createModbusTcpFrame(const QByteArray &rtuData)
{
    // TCP帧结构：事务处理标识(2字节) + 协议标识(2字节) + 长度(2字节) + 单元标识符 + 功能码 + 数据
    QByteArray tcpFrame;

    // 事务处理标识 (2字节) - 使用固定值0x0000
    tcpFrame.append(static_cast<char>(0x00));
    tcpFrame.append(static_cast<char>(0x00));

    // 协议标识 (2字节) - Modbus协议固定为0x0000
    tcpFrame.append(static_cast<char>(0x00));
    tcpFrame.append(static_cast<char>(0x00));

    // 长度字段 (2字节) - 后续字节数（不包括事务处理标识和协议标识）
    uint16_t length = rtuData.size();
    tcpFrame.append(static_cast<char>((length >> 8) & 0xFF)); // 长度高字节
    tcpFrame.append(static_cast<char>(length & 0xFF));        // 长度低字节

    // 添加RTU数据（不包括CRC）
    tcpFrame.append(rtuData);

    return tcpFrame;
}

QByteArray VoltageVerificationWorker::createModbusRtuFrame(const QString &hexCommand)
{
    // 移除所有空格
    QString cleanCommand = hexCommand.simplified().remove(' ');

    // 将十六进制字符串转换为 QByteArray
    QByteArray cmd = QByteArray::fromHex(cleanCommand.toLatin1());

    return cmd; // 返回不带CRC的RTU数据
}
